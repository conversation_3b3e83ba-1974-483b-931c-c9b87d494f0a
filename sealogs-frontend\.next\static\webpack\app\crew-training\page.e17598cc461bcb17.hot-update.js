"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match OverdueTrainingList structure\n    const transformTrainingListToOverdueFormat = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                }\n            };\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Create unified dataset when overdueToggle is excluded\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            const transformedCompletedTraining = transformTrainingListToOverdueFormat(trainingList || []);\n            return [\n                ...trainingSessionDues || [],\n                ...transformedCompletedTraining\n            ];\n        }\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 402,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 404,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Helper function to determine training data type\n    const getTrainingDataType = (training)=>{\n        if (training.date) return \"completed\";\n        if (training.dueDate || training.status) return \"overdue\";\n        return \"completed\" // default fallback\n        ;\n    };\n    // Helper function to get training types for different data structures\n    const getTrainingTypes = (training, dataType)=>{\n        if (dataType === \"completed\") {\n            var _training_trainingTypes;\n            return ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : _training_trainingTypes.nodes) || [];\n        } else {\n            return training.trainingType ? [\n                training.trainingType\n            ] : [];\n        }\n    };\n    // Helper function to get members for different data structures\n    const getMembers = (training, dataType)=>{\n        if (dataType === \"completed\") {\n            var _training_members;\n            return ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        } else {\n            return training.members || [];\n        }\n    };\n    // Unified column structure based on completed training table design\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: training,\n                    memberId: memberId,\n                    type: dataType === \"completed\" ? \"completed\" : \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.dueDate) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.dueDate) || 0).getTime();\n                return dateB - dateA;\n            }\n        },\n        {\n            accessorKey: \"trainingDrill\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training/drill\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_status;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                const trainingTypes = getTrainingTypes(training, dataType);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                    children: trainingTypes.length > 0 ? trainingTypes.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.title,\n                                index < trainingTypes.length - 1 ? \", \" : \"\"\n                            ]\n                        }, item.id || index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 35\n                        }, undefined)) : \"—\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _typesA_, _typesB_;\n                const dataTypeA = getTrainingDataType(rowA.original);\n                const dataTypeB = getTrainingDataType(rowB.original);\n                const typesA = getTrainingTypes(rowA.original, dataTypeA);\n                const typesB = getTrainingTypes(rowB.original, dataTypeB);\n                const valueA = ((_typesA_ = typesA[0]) === null || _typesA_ === void 0 ? void 0 : _typesA_.title) || \"\";\n                const valueB = ((_typesB_ = typesB[0]) === null || _typesB_ === void 0 ? void 0 : _typesB_.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"where\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Where\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_status, _training_vessel;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm text-nowrap\", dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                            children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"—\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 25\n                        }, undefined),\n                        dataType === \"completed\" && training.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                            vessel: training.vessel,\n                            iconClassName: \"size-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"who\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Who\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                const members = getMembers(training, dataType);\n                if (members.length === 0) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-muted-foreground\",\n                        children: \"—\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full flex items-end gap-1\",\n                    children: members.map((member, index)=>/*#__PURE__*/ {\n                        var _training_status;\n                        var _member_surname;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, member.id || index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 29\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                const dataTypeA = getTrainingDataType(rowA.original);\n                const dataTypeB = getTrainingDataType(rowB.original);\n                const membersA = getMembers(rowA.original, dataTypeA);\n                const membersB = getMembers(rowB.original, dataTypeB);\n                var _membersA__firstName, _membersA__surname;\n                const valueA = membersA[0] ? \"\".concat((_membersA__firstName = membersA[0].firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA[0].surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") : \"\";\n                var _membersB__firstName, _membersB__surname;\n                const valueB = membersB[0] ? \"\".concat((_membersB__firstName = membersB[0].firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB[0].surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") : \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 607,\n                columnNumber: 13\n            }, undefined),\n            excludeFilters.includes(\"overdueToggle\") ? isUnifiedDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 619,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: getUnifiedTrainingData(),\n                hideCrewColumn: true,\n                pageSize: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 624,\n                columnNumber: 21\n            }, undefined) : overdueBoolean ? trainingSessionDuesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading overdue training...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 632,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: trainingSessionDues\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 637,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (trainingList === null || trainingList === void 0 ? void 0 : trainingList.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: trainingList,\n                    pageSize: 20,\n                    onChange: handleFilterChange,\n                    showToolbar: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"group border-b hover: \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"  \",\n                                    children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 652,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 749,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 606,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, hideCrewColumn = false, pageSize = 20 } = param;\n    // Helper functions for overdue/upcoming training data\n    const getTrainingDataType = (training)=>{\n        if (training.date) return \"completed\";\n        if (training.dueDate || training.status) return \"overdue\";\n        return \"overdue\" // default for this component\n        ;\n    };\n    const getTrainingTypes = (training, dataType)=>{\n        if (dataType === \"completed\") {\n            var _training_trainingTypes;\n            return ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : _training_trainingTypes.nodes) || [];\n        } else {\n            return training.trainingType ? [\n                training.trainingType\n            ] : [];\n        }\n    };\n    const getMembers = (training, dataType)=>{\n        if (dataType === \"completed\") {\n            var _training_members;\n            return ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n        } else {\n            return training.members || [];\n        }\n    };\n    // Use the same unified column structure as the main component\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 796,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: training,\n                    type: dataType === \"completed\" ? \"completed\" : \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 802,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.dueDate) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.dueDate) || 0).getTime();\n                return dateB - dateA;\n            }\n        },\n        {\n            accessorKey: \"trainingDrill\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training/drill\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_status;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                const trainingTypes = getTrainingTypes(training, dataType);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                    children: trainingTypes.length > 0 ? trainingTypes.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.title,\n                                index < trainingTypes.length - 1 ? \", \" : \"\"\n                            ]\n                        }, item.id || index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 35\n                        }, undefined)) : \"—\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 833,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _typesA_, _typesB_;\n                const dataTypeA = getTrainingDataType(rowA.original);\n                const dataTypeB = getTrainingDataType(rowB.original);\n                const typesA = getTrainingTypes(rowA.original, dataTypeA);\n                const typesB = getTrainingTypes(rowB.original, dataTypeB);\n                const valueA = ((_typesA_ = typesA[0]) === null || _typesA_ === void 0 ? void 0 : _typesA_.title) || \"\";\n                const valueB = ((_typesB_ = typesB[0]) === null || _typesB_ === void 0 ? void 0 : _typesB_.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"where\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Where\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_status, _training_vessel;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm text-nowrap\", dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                            children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"—\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 875,\n                            columnNumber: 25\n                        }, undefined),\n                        dataType === \"completed\" && training.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                            vessel: training.vessel,\n                            iconClassName: \"size-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 887,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 874,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"who\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Who\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 905,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                const dataType = getTrainingDataType(training);\n                const members = getMembers(training, dataType);\n                if (members.length === 0) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-muted-foreground\",\n                        children: \"—\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full flex items-end gap-1\",\n                    children: members.map((member, index)=>/*#__PURE__*/ {\n                        var _training_status;\n                        var _member_surname;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: dataType === \"overdue\" && ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, member.id || index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 29\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 918,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                const dataTypeA = getTrainingDataType(rowA.original);\n                const dataTypeB = getTrainingDataType(rowB.original);\n                const membersA = getMembers(rowA.original, dataTypeA);\n                const membersB = getMembers(rowB.original, dataTypeB);\n                var _membersA__firstName, _membersA__surname;\n                const valueA = membersA[0] ? \"\".concat((_membersA__firstName = membersA[0].firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA[0].surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") : \"\";\n                var _membersB__firstName, _membersB__surname;\n                const valueB = membersB[0] ? \"\".concat((_membersB__firstName = membersB[0].firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB[0].surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") : \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"who\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 970,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"!w-[75px] h-auto\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 147 147.01\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                    fill: \"#052350\",\n                                    fillRule: \"evenodd\",\n                                    opacity: \".97\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                    fill: \"#024450\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                    fill: \"#052451\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1026,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"  \",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 979,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 978,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 977,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});