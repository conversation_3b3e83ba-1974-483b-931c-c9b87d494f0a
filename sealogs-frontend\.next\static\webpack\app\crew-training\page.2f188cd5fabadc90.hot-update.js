"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match OverdueTrainingList structure\n    const transformTrainingListToOverdueFormat = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                }\n            };\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Create unified dataset when overdueToggle is excluded\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            const transformedCompletedTraining = transformTrainingListToOverdueFormat(trainingList || []);\n            return [\n                ...trainingSessionDues || [],\n                ...transformedCompletedTraining\n            ];\n        }\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 402,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 404,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Unified column definitions that work with both completed and overdue/upcoming data\n    const createUnifiedColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date / Training\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    const isCompleted = training.date || training.category === \"completed\";\n                    const isOverdue = (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: isCompleted ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                    // Handle both completed (date) and overdue/upcoming (dueDate) data\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            },\n            {\n                accessorKey: \"trainingDrillsCompleted\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drills\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_trainingTypes, _training_trainingType;\n                    const training = row.original;\n                    const isOverdue = (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue;\n                    // Handle both completed (trainingTypes.nodes) and overdue/upcoming (trainingType) data\n                    let trainingTitle = \"\";\n                    if ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : _training_trainingTypes.nodes) {\n                        // Completed training data structure\n                        trainingTitle = training.trainingTypes.nodes.map((item)=>item.title).join(\", \");\n                    } else if ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) {\n                        // Overdue/upcoming training data structure\n                        trainingTitle = training.trainingType.title;\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(isOverdue && \"text-cinnabar-500 hover:text-cinnabar-700\"),\n                        children: trainingTitle || \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingTypes_nodes_, _rowA_original_trainingTypes_nodes, _rowA_original_trainingTypes, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingTypes_nodes_, _rowB_original_trainingTypes_nodes, _rowB_original_trainingTypes, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    // Handle both data structures for sorting\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingTypes = _rowA_original.trainingTypes) === null || _rowA_original_trainingTypes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes = _rowA_original_trainingTypes.nodes) === null || _rowA_original_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes_ = _rowA_original_trainingTypes_nodes[0]) === null || _rowA_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingTypes = _rowB_original.trainingTypes) === null || _rowB_original_trainingTypes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes = _rowB_original_trainingTypes.nodes) === null || _rowB_original_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes_ = _rowB_original_trainingTypes_nodes[0]) === null || _rowB_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"where\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    var _training_trainer_surname, _training_trainer_surname1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        training.trainer.firstName,\n                                        \" \",\n                                        (_training_trainer_surname = training.trainer.surname) !== null && _training_trainer_surname !== void 0 ? _training_trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 33\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        training.trainer.firstName,\n                                        \" \",\n                                        (_training_trainer_surname1 = training.trainer.surname) !== null && _training_trainer_surname1 !== void 0 ? _training_trainer_surname1 : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowA_original_trainer, _rowA_original1, _rowA_original2, _rowA_original_trainer1, _rowA_original3, _rowB_original, _rowB_original_trainer, _rowB_original1, _rowB_original2, _rowB_original_trainer1, _rowB_original3;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainer = _rowA_original1.trainer) === null || _rowA_original_trainer === void 0 ? void 0 : _rowA_original_trainer.firstName), \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original2 = rowA.original) === null || _rowA_original2 === void 0 ? void 0 : _rowA_original2.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original3 = rowA.original) === null || _rowA_original3 === void 0 ? void 0 : (_rowA_original_trainer1 = _rowA_original3.trainer) === null || _rowA_original_trainer1 === void 0 ? void 0 : _rowA_original_trainer1.surname)) || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainer = _rowB_original1.trainer) === null || _rowB_original_trainer === void 0 ? void 0 : _rowB_original_trainer.firstName), \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original2 = rowB.original) === null || _rowB_original2 === void 0 ? void 0 : _rowB_original2.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original3 = rowB.original) === null || _rowB_original3 === void 0 ? void 0 : (_rowB_original_trainer1 = _rowB_original3.trainer) === null || _rowB_original_trainer1 === void 0 ? void 0 : _rowB_original_trainer1.surname)) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"who\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex items-end gap-1\",\n                        children: training.members.nodes.map((member, index)=>{\n                            var _member_surname;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                            size: \"sm\",\n                                            variant: \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 53\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 45\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 45\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 41\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                    const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                    var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                    const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 636,\n                columnNumber: 13\n            }, undefined),\n            excludeFilters.includes(\"overdueToggle\") ? isUnifiedDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 648,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: getUnifiedTrainingData(),\n                hideCrewColumn: true,\n                pageSize: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 653,\n                columnNumber: 21\n            }, undefined) : overdueBoolean ? trainingSessionDuesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading overdue training...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 661,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: trainingSessionDues\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 666,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (trainingList === null || trainingList === void 0 ? void 0 : trainingList.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: trainingList,\n                    pageSize: 20,\n                    onChange: handleFilterChange,\n                    showToolbar: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"group border-b hover: \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"  \",\n                                    children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 681,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 778,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 635,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 806,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 819,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 842,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 898,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns1 = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns1,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 915,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"!w-[75px] h-auto\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 147 147.01\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                    fill: \"#052350\",\n                                    fillRule: \"evenodd\",\n                                    opacity: \".97\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                    fill: \"#024450\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                    fill: \"#052451\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 986,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 991,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"  \",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 924,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 923,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 922,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});