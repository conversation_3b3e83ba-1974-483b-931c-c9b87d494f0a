"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_vessel, _data_originalData;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center landscape:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                className: \"text-sm m-0 text-muted-foreground\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium px-2 py-1 rounded-md\", data.status.isOverdue ? \"bg-destructive/10 text-destructive\" : data.status.dueWithinSevenDays ? \"bg-warning/10 text-warning\" : \"bg-muted/50 text-muted-foreground\"),\n                                children: data.status.label || data.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"tablet-md:hidden space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Training Details:\" : \"Due Date:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm\", !isCompleted && getStatusColorClasses(data)),\n                        children: isCompleted ? trainingTitle : data.dueDate ? formatDate(data.dueDate) : \"Not specified\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm laptop:hidden text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 29\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 49\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 135,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between landscape:hidden items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 208,\n                columnNumber: 13\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                        className: \"my-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Category: \",\n                                    data.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 25\n                            }, undefined),\n                            data.dueDate && (()=>{\n                                const today = new Date();\n                                const dueDate = new Date(data.dueDate);\n                                const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: daysDifference > 0 ? \"Due in \".concat(daysDifference, \" days\") : daysDifference === 0 ? \"Due today\" : \"\".concat(Math.abs(daysDifference), \" days overdue\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 37\n                                }, undefined);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isCompleted && ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : _data_originalData.trainer) && !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 253,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 83,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 589,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 596,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});